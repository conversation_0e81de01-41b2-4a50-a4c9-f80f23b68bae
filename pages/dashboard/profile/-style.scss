
/* start:: register_main_wrapper */
.register_main_wrapper {
  width: 80%;
  margin-inline: auto;
  padding-bottom: 90px;
  @media (max-width: 991px) {
    width: 95%;
    padding-bottom: 40px;
  }
  .title_box {
    margin-bottom: 50px;
    h2 {
      font-size: 30px;
      font-weight: 600;
      margin-bottom: 0;
    }
  }
  .card_footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #fff;
    padding-block: 20px;
    border-top: 1px solid #f2f4f7;
    z-index: 1001;
    .buttons_wrapper {
      width: 80%;
      margin-inline: auto;
      display: flex;
      justify-content: flex-end;
      .btn {
        &:last-child {
          background-color: $base-color;
          color: #fff;
          border-radius: 8px;
        }
      }
    }
  }
  .password-toggle {
    border: none !important;
    background: transparent !important;
    padding: 0.375rem 0.75rem;
    color: #6c757d;
    &:hover {
      color: #495057;
      background: transparent !important;
    }
    &:focus {
      box-shadow: none !important;
      background: transparent !important;
    }
    .icon {
      width: 12px;
      height: 12px;
    }
  }

  .input-group {
    &.has-icon-append {
      .form-control {
        padding-inline-end: 50px; // Adds space for the icon
      }
      .input-group-append {
        position: absolute;
        top: 50%;
        right: 8px;
        transform: translateY(-50%);
        z-index: 10;
        height: 44px;
        display: flex;
        align-items: center;
        padding-inline-start: 20px;
        padding-inline-end: 20px;
        .icon.password-toggle-icon {
          fill: #667085;
          stroke: #667085;
          cursor: pointer;
          transition: all 0.25s ease;
          &:hover {
            fill: #495057;
            stroke: #495057;
          }
          &.invalid {
            fill: #cc0000;
            stroke: #cc0000;
          }
        }
      }
    }
  }
}



[dir='rtl'] {
  .input_flex {
    flex-direction: row-reverse !important;
  }
  .register_main_wrapper .input-group.has-icon-append .input-group-append {
    right: unset;
    left: 8px;
  }
}
